/**
 * 管道路径数据
 * 从 Blender 中提取的管道中心线路径
 * 可用于相机路径、对象移动轨迹等
 */

// 管道路径点数组 [x, y, z]
export const PIPE_PATH_COORDINATES = [
  [-15.58, 0.06, -0.77],
  [-14.52, -0.61, -0.78],
  [-13.44, -1.29, -0.80],
  [-12.48, -1.81, -0.80],
  [-11.38, -2.01, -0.79],
  [-10.12, -2.21, -0.70],
  [-8.85, -2.42, -0.61],
  [-7.65, -2.59, -0.52],
  [-6.43, -2.71, -0.46],
  [-5.20, -2.83, -0.38],
  [-3.97, -2.95, -0.30],
  [-2.74, -3.07, -0.22],
  [-1.51, -3.19, -0.14],
  [-0.28, -3.31, -0.06],
  [0.95, -3.43, 0.02],
  [2.18, -3.55, 0.10],
  [3.41, -3.67, 0.18],
  [4.64, -3.79, 0.26],
  [5.87, -3.91, 0.34],
  [7.10, -4.03, 0.42],
  [8.33, -4.15, 0.50],
  [9.56, -4.27, 0.58],
  [10.79, -4.39, 0.66],
  [12.02, -4.51, 0.74],
  [13.25, -4.63, 0.82],
  [14.48, -4.75, 0.90],
  [15.71, -4.87, 0.98],
  [16.94, -4.99, 1.06],
  [18.17, -5.11, 1.14],
  [19.40, -5.23, 1.22]
]

// 路径元数据
export const PIPE_PATH_METADATA = {
  name: 'BlenderPipePath',
  totalPoints: 30,
  originalVertices: 6949,
  extractionMethod: 'center_line_averaging',
  coordinateSystem: 'world_space',
  mainDirection: 'x_axis',
  pathLength: calculatePathLength(),
  boundingBox: {
    min: [-15.58, -5.23, -0.80],
    max: [19.40, 0.06, 1.22],
    size: [34.98, 5.29, 2.02]
  }
}

/**
 * 计算路径总长度
 * @returns {number} 路径长度
 */
function calculatePathLength() {
  let length = 0
  for (let i = 1; i < PIPE_PATH_COORDINATES.length; i++) {
    const prev = PIPE_PATH_COORDINATES[i - 1]
    const curr = PIPE_PATH_COORDINATES[i]
    const dx = curr[0] - prev[0]
    const dy = curr[1] - prev[1]
    const dz = curr[2] - prev[2]
    length += Math.sqrt(dx * dx + dy * dy + dz * dz)
  }
  return length
}

/**
 * 获取路径上指定索引的点
 * @param {number} index - 点索引
 * @returns {Array|null} [x, y, z] 坐标或 null
 */
export function getPathPoint(index) {
  if (index >= 0 && index < PIPE_PATH_COORDINATES.length) {
    return [...PIPE_PATH_COORDINATES[index]]
  }
  return null
}

/**
 * 获取路径起点
 * @returns {Array} [x, y, z] 坐标
 */
export function getPathStart() {
  return [...PIPE_PATH_COORDINATES[0]]
}

/**
 * 获取路径终点
 * @returns {Array} [x, y, z] 坐标
 */
export function getPathEnd() {
  return [...PIPE_PATH_COORDINATES[PIPE_PATH_COORDINATES.length - 1]]
}

/**
 * 获取路径中点
 * @returns {Array} [x, y, z] 坐标
 */
export function getPathCenter() {
  const midIndex = Math.floor(PIPE_PATH_COORDINATES.length / 2)
  return [...PIPE_PATH_COORDINATES[midIndex]]
}

/**
 * 线性插值获取路径上的点
 * @param {number} t - 参数 (0-1)
 * @returns {Array} [x, y, z] 坐标
 */
export function getPathPointByParameter(t) {
  t = Math.max(0, Math.min(1, t)) // 限制在 0-1 范围内
  
  const segmentFloat = t * (PIPE_PATH_COORDINATES.length - 1)
  const segmentIndex = Math.floor(segmentFloat)
  const segmentT = segmentFloat - segmentIndex
  
  if (segmentIndex >= PIPE_PATH_COORDINATES.length - 1) {
    return [...PIPE_PATH_COORDINATES[PIPE_PATH_COORDINATES.length - 1]]
  }
  
  const p1 = PIPE_PATH_COORDINATES[segmentIndex]
  const p2 = PIPE_PATH_COORDINATES[segmentIndex + 1]
  
  return [
    p1[0] + (p2[0] - p1[0]) * segmentT,
    p1[1] + (p2[1] - p1[1]) * segmentT,
    p1[2] + (p2[2] - p1[2]) * segmentT
  ]
}

/**
 * 获取路径方向向量
 * @param {number} index - 点索引
 * @returns {Array} [x, y, z] 方向向量
 */
export function getPathDirection(index) {
  if (index < 0 || index >= PIPE_PATH_COORDINATES.length - 1) {
    return [1, 0, 0] // 默认 X 方向
  }
  
  const curr = PIPE_PATH_COORDINATES[index]
  const next = PIPE_PATH_COORDINATES[index + 1]
  
  const dx = next[0] - curr[0]
  const dy = next[1] - curr[1]
  const dz = next[2] - curr[2]
  
  // 归一化
  const length = Math.sqrt(dx * dx + dy * dy + dz * dz)
  if (length === 0) return [1, 0, 0]
  
  return [dx / length, dy / length, dz / length]
}

/**
 * 将路径应用到 orbitControls target 的辅助函数
 * @param {Object} orbitControls - Three.js OrbitControls 实例
 * @param {number} t - 路径参数 (0-1)
 */
export function setOrbitTargetToPath(orbitControls, t) {
  const point = getPathPointByParameter(t)
  orbitControls.target.set(point[0], point[1], point[2])
  orbitControls.update()
}

/**
 * 创建路径动画的辅助函数
 * @param {Object} orbitControls - Three.js OrbitControls 实例
 * @param {number} duration - 动画持续时间（毫秒）
 * @param {Function} onUpdate - 更新回调函数
 * @param {Function} onComplete - 完成回调函数
 */
export function animateAlongPath(orbitControls, duration = 5000, onUpdate = null, onComplete = null) {
  const startTime = Date.now()
  
  function animate() {
    const elapsed = Date.now() - startTime
    const t = Math.min(elapsed / duration, 1)
    
    setOrbitTargetToPath(orbitControls, t)
    
    if (onUpdate) {
      onUpdate(t, getPathPointByParameter(t))
    }
    
    if (t < 1) {
      requestAnimationFrame(animate)
    } else if (onComplete) {
      onComplete()
    }
  }
  
  animate()
}

// 预设的路径位置
export const PATH_PRESETS = {
  START: getPathStart(),
  CENTER: getPathCenter(),
  END: getPathEnd(),
  QUARTER: getPathPointByParameter(0.25),
  THREE_QUARTER: getPathPointByParameter(0.75)
}

export default {
  PIPE_PATH_COORDINATES,
  PIPE_PATH_METADATA,
  getPathPoint,
  getPathStart,
  getPathEnd,
  getPathCenter,
  getPathPointByParameter,
  getPathDirection,
  setOrbitTargetToPath,
  animateAlongPath,
  PATH_PRESETS
}
