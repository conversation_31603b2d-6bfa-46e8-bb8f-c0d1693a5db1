<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="031b2ea2-4304-4e1f-808f-ba85151d3cd4" name="更改" comment="feat: 添加去首部、尾部快捷按钮">
      <change afterPath="$PROJECT_DIR$/public/model/管道线-路径.glb" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/assets/center-line/A01.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/assets/center-line/path_points.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/api/deviceManage.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/api/deviceManage.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/api/personal.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/api/personal.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/assets/linePoints.json" beforeDir="false" afterPath="$PROJECT_DIR$/src/assets/center-line/linePoints.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/aobo/fieldView/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/aobo/fieldView/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/aobo/fieldView/threeJs/service/environment.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/aobo/fieldView/threeJs/service/environment.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/vue.config.js" beforeDir="false" afterPath="$PROJECT_DIR$/vue.config.js" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="30zJLl0puAkEZFoR5CtEhU3Lr9K" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager.252": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "main",
    "junie.onboarding.icon.badge.shown": "true",
    "last_opened_file_path": "E:/Vankey/power-grid/csg-dts-web/public/model",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "npm.dev.executor": "Run",
    "to.speed.mode.migration.done": "true",
    "ts.external.directory.path": "D:\\JetBrains\\WebStorm 2024.1.3\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\Vankey\power-grid\csg-dts-web\public\model" />
      <recent name="E:\Vankey\power-grid\csg-dts-web\src\assets\center-line" />
      <recent name="E:\Vankey\power-grid\csg-dts-web\public" />
      <recent name="E:\Vankey\power-grid\csg-dts-web\src\store\modules" />
      <recent name="E:\Vankey\power-grid\csg-dts-web\src\views\aobo\fieldView" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="E:\Vankey\power-grid\csg-dts-web\src\assets\center-line" />
      <recent name="E:\Vankey\power-grid\csg-dts-web\src\assets" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="dev" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="npm.dev" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-e03c56caf84a-JavaScript-WS-252.23892.411" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="031b2ea2-4304-4e1f-808f-ba85151d3cd4" name="更改" comment="" />
      <created>1754624415863</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754624415863</updated>
      <workItem from="1754624418213" duration="5939000" />
      <workItem from="1754874806925" duration="1369000" />
      <workItem from="1754876368486" duration="2173000" />
      <workItem from="1754878594105" duration="21158000" />
      <workItem from="1754961115606" duration="16777000" />
      <workItem from="1755056249347" duration="8305000" />
    </task>
    <task id="LOCAL-00001" summary="feat: 项目初始化">
      <option name="closed" value="true" />
      <created>1754642645649</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1754642645649</updated>
    </task>
    <task id="LOCAL-00002" summary="feat: 引入模型并添加加载动画">
      <option name="closed" value="true" />
      <created>1754895710626</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1754895710626</updated>
    </task>
    <task id="LOCAL-00003" summary="feat: 全屏时触发画布尺寸更新">
      <option name="closed" value="true" />
      <created>1754896011074</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1754896011074</updated>
    </task>
    <task id="LOCAL-00004" summary="feat: 部分模型更换材质">
      <option name="closed" value="true" />
      <created>1754906158292</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1754906158292</updated>
    </task>
    <task id="LOCAL-00005" summary="feat: 性能优化">
      <option name="closed" value="true" />
      <created>1754967581343</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1754967581343</updated>
    </task>
    <task id="LOCAL-00006" summary="feat: 添加去首部、尾部快捷按钮">
      <option name="closed" value="true" />
      <created>1754969575996</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1754969575996</updated>
    </task>
    <option name="localTasksCounter" value="7" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="feat: 项目初始化" />
    <MESSAGE value="feat: 引入模型并添加加载动画" />
    <MESSAGE value="feat: 全屏时触发画布尺寸更新" />
    <MESSAGE value="feat: 部分模型更换材质" />
    <MESSAGE value="feat: 性能优化" />
    <MESSAGE value="feat: 添加去首部、尾部快捷按钮" />
    <option name="LAST_COMMIT_MESSAGE" value="feat: 添加去首部、尾部快捷按钮" />
  </component>
</project>